#!/usr/bin/env node

/**
 * 🚀 Quick Build Script
 * Bypass workspace issues và build trực tiếp với EAS
 */

const { execSync } = require('child_process');

console.log('🚀 Quick Build - Bypassing workspace issues...\n');

function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    return false;
  }
}

function main() {
  console.log('🎯 Building production APK without workspace dependencies...\n');

  // 1. Check EAS login
  console.log('🔐 Checking EAS authentication...');
  try {
    execSync('eas whoami', { stdio: 'pipe' });
    console.log('✅ EAS authenticated\n');
  } catch (error) {
    console.log('❌ Not logged in to EAS. Please run: eas login');
    process.exit(1);
  }

  // 2. Build directly with EAS (cloud build)
  console.log('☁️ Starting EAS cloud build...');
  console.log('This bypasses local workspace issues by building in the cloud.\n');
  
  const buildSuccess = runCommand(
    'eas build --platform android --profile production-apk --non-interactive',
    'Building production APK with EAS'
  );

  if (buildSuccess) {
    console.log('🎉 Build completed successfully!');
    console.log('');
    console.log('📱 Next steps:');
    console.log('1. Download APK from the link provided above');
    console.log('2. Install on your Android device');
    console.log('3. Test all features');
    console.log('');
    console.log('🔧 If APK doesn\'t work on device:');
    console.log('- Check Android version (7.0+ required)');
    console.log('- Enable "Install from unknown sources"');
    console.log('- Grant all permissions when prompted');
    console.log('- Use adb logcat to debug: adb logcat | grep Workly');
  } else {
    console.log('❌ Build failed. Trying alternative approach...\n');
    
    // Alternative: Try with cache clear
    console.log('🔄 Trying with cache clear...');
    runCommand(
      'eas build --platform android --profile production-apk --clear-cache',
      'Building with cache clear'
    );
  }
}

if (require.main === module) {
  main();
}
