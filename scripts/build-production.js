#!/usr/bin/env node

/**
 * 🏗️ Production Build Script
 * Script tối ưu để build APK production hoạt động trên thiết bị thật
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Production Build Script for Workly\n');

// Kiểm tra môi trường
function checkEnvironment() {
  console.log('🔍 Checking environment...');
  
  try {
    // Kiểm tra EAS CLI
    execSync('eas --version', { stdio: 'pipe' });
    console.log('✅ EAS CLI is installed');
  } catch (error) {
    console.log('❌ EAS CLI not found. Installing...');
    execSync('npm install -g @expo/eas-cli', { stdio: 'inherit' });
  }

  // Kiểm tra login
  try {
    execSync('eas whoami', { stdio: 'pipe' });
    console.log('✅ EAS authenticated');
  } catch (error) {
    console.log('❌ Not logged in to EAS. Please run: eas login');
    process.exit(1);
  }

  console.log('');
}

// Chuẩn bị build
function prepareBuild() {
  console.log('📦 Preparing build...');
  
  // Clear cache
  console.log('🧹 Clearing cache...');
  try {
    execSync('npx expo start --clear', { stdio: 'pipe', timeout: 5000 });
  } catch (error) {
    // Timeout expected, just clearing cache
  }
  
  // Install dependencies
  console.log('📥 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  
  console.log('✅ Build preparation complete\n');
}

// Build APK
function buildAPK() {
  console.log('🏗️ Building production APK...');
  console.log('This may take 10-20 minutes...\n');
  
  try {
    execSync('eas build --platform android --profile production-apk', { 
      stdio: 'inherit',
      timeout: 1800000 // 30 minutes timeout
    });
    console.log('\n✅ APK build completed successfully!');
  } catch (error) {
    console.log('\n❌ Build failed. Check the error above.');
    process.exit(1);
  }
}

// Hướng dẫn sau build
function postBuildInstructions() {
  console.log('\n📱 Next Steps:');
  console.log('1. Download APK from the link provided above');
  console.log('2. Enable "Install from unknown sources" on your Android device');
  console.log('3. Transfer APK to your device and install');
  console.log('4. Test all features on the device');
  console.log('');
  console.log('🔧 If APK doesn\'t work:');
  console.log('- Check device Android version (minimum 7.0)');
  console.log('- Ensure all permissions are granted');
  console.log('- Try clearing app data and restart');
  console.log('- Check logcat for error messages');
  console.log('');
  console.log('💡 For debugging:');
  console.log('- Use: adb logcat | grep Workly');
  console.log('- Or: adb logcat | grep ReactNative');
}

// Main function
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: npm run build:production');
    console.log('       node scripts/build-production.js');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h    Show this help message');
    console.log('  --skip-prep   Skip preparation steps');
    return;
  }

  checkEnvironment();
  
  if (!args.includes('--skip-prep')) {
    prepareBuild();
  }
  
  buildAPK();
  postBuildInstructions();
}

// Run script
if (require.main === module) {
  main();
}

module.exports = { checkEnvironment, prepareBuild, buildAPK };
