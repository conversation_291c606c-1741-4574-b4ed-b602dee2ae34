#!/usr/bin/env node

/**
 * 🏗️ Setup Local Build Environment
 * <PERSON><PERSON><PERSON> bị môi trường để build với Android Studio
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏗️ Setting up local build environment...\n');

function runCommand(command, description, options = {}) {
  console.log(`📦 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: options.cwd || process.cwd(),
      timeout: options.timeout || 120000
    });
    console.log(`✅ ${description} completed\n`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    if (options.required) {
      console.log('This step is required. Please fix the error and try again.');
      process.exit(1);
    }
    console.log('Continuing with next step...\n');
    return false;
  }
}

function checkEnvironment() {
  console.log('🔍 Checking environment...\n');
  
  // Check Node.js
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Node.js: ${nodeVersion}`);
  } catch (error) {
    console.log('❌ Node.js not found');
  }

  // Check Java
  try {
    const javaVersion = execSync('java -version', { encoding: 'utf8', stderr: 'inherit' });
    console.log('✅ Java is installed');
  } catch (error) {
    console.log('❌ Java not found. Please install JDK 11+');
  }

  // Check Android SDK
  const androidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;
  if (androidHome && fs.existsSync(androidHome)) {
    console.log(`✅ Android SDK: ${androidHome}`);
  } else {
    console.log('❌ Android SDK not found. Please set ANDROID_HOME environment variable');
  }

  console.log('');
}

function setupProject() {
  console.log('🚀 Setting up project for local build...\n');

  // 1. Clean project
  console.log('🧹 Cleaning project...');
  if (fs.existsSync('android')) {
    fs.rmSync('android', { recursive: true, force: true });
    console.log('✅ Removed existing android folder');
  }
  if (fs.existsSync('ios')) {
    fs.rmSync('ios', { recursive: true, force: true });
    console.log('✅ Removed existing ios folder');
  }

  // 2. Install dependencies
  runCommand('npm install', 'Installing dependencies', { required: true });

  // 3. Generate native projects
  runCommand('npx expo prebuild --platform android', 'Generating Android project', { 
    required: true,
    timeout: 180000 
  });

  // 4. Verify Android project
  if (fs.existsSync('android/app/build.gradle')) {
    console.log('✅ Android project generated successfully');
  } else {
    console.log('❌ Android project generation failed');
    process.exit(1);
  }
}

function buildInstructions() {
  console.log('📋 Next Steps - Build with Android Studio:\n');
  
  console.log('🔧 Method 1: Using Gradle Command Line');
  console.log('cd android');
  console.log('gradlew assembleRelease');
  console.log('');
  
  console.log('🔧 Method 2: Using Android Studio GUI');
  console.log('1. Open Android Studio');
  console.log('2. Open project: Select the "android" folder');
  console.log('3. Wait for Gradle sync to complete');
  console.log('4. Build > Generate Signed Bundle/APK');
  console.log('5. Choose APK > Next');
  console.log('6. Create new keystore or use existing');
  console.log('7. Build Release APK');
  console.log('');
  
  console.log('🔧 Method 3: Using Expo CLI');
  console.log('npx expo run:android --variant release');
  console.log('');
  
  console.log('📱 APK Location:');
  console.log('android/app/build/outputs/apk/release/app-release.apk');
  console.log('');
  
  console.log('🔍 Debugging:');
  console.log('- Check Android Studio Build Output');
  console.log('- View Gradle Console for errors');
  console.log('- Use adb logcat for runtime debugging');
  console.log('');
  
  console.log('⚠️ Common Issues:');
  console.log('- Ensure ANDROID_HOME is set correctly');
  console.log('- Use JDK 11 (not newer versions)');
  console.log('- Accept Android SDK licenses: sdkmanager --licenses');
  console.log('- Clear Gradle cache if build fails: gradlew clean');
}

function main() {
  console.log('🏗️ Local Build Setup for Workly\n');
  
  checkEnvironment();
  setupProject();
  buildInstructions();
  
  console.log('✨ Setup completed! Ready for local build.');
}

if (require.main === module) {
  main();
}
