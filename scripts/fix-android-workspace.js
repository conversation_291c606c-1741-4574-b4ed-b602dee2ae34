#!/usr/bin/env node

/**
 * 🔧 Fix Android Workspace Issues
 * Khắc phục lỗi Eclipse/Java workspace và Android build issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Android workspace issues...\n');

function runCommand(command, description, options = {}) {
  console.log(`📦 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: options.cwd || process.cwd(),
      timeout: options.timeout || 60000
    });
    console.log(`✅ ${description} completed\n`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    console.log('Continuing with next step...\n');
    return false;
  }
}

function removeDirectory(dirPath, description) {
  console.log(`🗑️ ${description}...`);
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ ${description} completed\n`);
    } else {
      console.log(`ℹ️ ${dirPath} does not exist, skipping\n`);
    }
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    console.log('Continuing with next step...\n');
  }
}

function main() {
  console.log('🚀 Starting Android workspace fix...\n');

  // 1. Remove problematic directories
  console.log('🧹 Cleaning workspace...');
  removeDirectory('android', 'Removing android directory');
  removeDirectory('ios', 'Removing ios directory');
  removeDirectory('.expo', 'Removing .expo cache');
  removeDirectory('node_modules/.cache', 'Removing node_modules cache');

  // 2. Clean npm cache
  runCommand('npm cache clean --force', 'Cleaning npm cache');

  // 3. Reinstall dependencies
  runCommand('npm install', 'Reinstalling dependencies');

  // 4. Fix Expo dependencies
  runCommand('npx expo install --fix', 'Fixing Expo dependencies');

  // 5. Regenerate native directories
  console.log('🏗️ Regenerating native projects...');
  runCommand('npx expo prebuild --clean', 'Regenerating Android/iOS projects', { timeout: 180000 });

  // 6. Verify Android project structure
  console.log('🔍 Verifying Android project...');
  const androidPath = path.join(process.cwd(), 'android');
  if (fs.existsSync(androidPath)) {
    console.log('✅ Android project regenerated successfully');
    
    // Check key files
    const keyFiles = [
      'android/app/build.gradle',
      'android/build.gradle',
      'android/gradle.properties',
      'android/settings.gradle'
    ];
    
    keyFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ ${file} exists`);
      } else {
        console.log(`❌ ${file} missing`);
      }
    });
  } else {
    console.log('❌ Android project not generated');
  }

  console.log('\n🎯 Android workspace fix completed!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('1. Close and reopen VS Code');
  console.log('2. Run: npm run build:production');
  console.log('3. Or try: eas build --platform android --profile production-apk');
  console.log('');
  console.log('🔧 If issues persist:');
  console.log('- Restart your computer');
  console.log('- Check Windows permissions on the project folder');
  console.log('- Try running VS Code as administrator');
  console.log('- Use Windows PowerShell instead of Command Prompt');
}

if (require.main === module) {
  main();
}
