#!/usr/bin/env node

/**
 * 🔧 Fix Dependencies Script
 * S<PERSON>a các vấn đề dependencies và cài đặt phiên bản tương thích
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing dependencies for production build...\n');

function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    console.log('Continuing with next step...\n');
  }
}

function main() {
  // 1. Clear cache và node_modules
  console.log('🧹 Cleaning project...');
  runCommand('rm -rf node_modules package-lock.json', 'Removing node_modules and lock file');
  
  // 2. Cài đặt dependencies với phiên bản tương thích
  console.log('📥 Installing compatible dependencies...');
  runCommand('npm install', 'Installing dependencies');
  
  // 3. Cài đặt React 18 tương thích
  runCommand('npm install react@18.3.1 @types/react@18.3.12', 'Installing compatible React version');
  
  // 4. Fix Expo dependencies
  runCommand('npx expo install --fix', 'Fixing Expo dependencies');
  
  // 5. Prebuild để kiểm tra native dependencies
  console.log('🔍 Checking native dependencies...');
  try {
    execSync('npx expo prebuild --clean', { stdio: 'inherit' });
    console.log('✅ Native dependencies check passed\n');
  } catch (error) {
    console.log('⚠️ Prebuild had issues, but continuing...\n');
  }
  
  // 6. Kiểm tra cấu hình
  console.log('⚙️ Validating configuration...');
  
  // Kiểm tra app.json
  try {
    const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    console.log('✅ app.json is valid');
  } catch (error) {
    console.log('❌ app.json has issues:', error.message);
  }
  
  // Kiểm tra eas.json
  try {
    const easJson = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
    console.log('✅ eas.json is valid');
  } catch (error) {
    console.log('❌ eas.json has issues:', error.message);
  }
  
  console.log('\n🎯 Dependencies fix completed!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('1. Run: npm run build:production');
  console.log('2. Or: eas build --platform android --profile production-apk');
  console.log('3. Download and test APK on device');
  console.log('');
  console.log('🔧 If build still fails:');
  console.log('- Check EAS build logs for specific errors');
  console.log('- Ensure Android SDK is properly configured');
  console.log('- Try building with --clear-cache flag');
}

if (require.main === module) {
  main();
}
