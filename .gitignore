# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

<<<<<<< HEAD
# Prebuild
/android/
/ios/

=======
>>>>>>> 229e83784fd46ef5ffd97c8f8212653c1ebc9d7f
# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo
<<<<<<< HEAD

# IDE
.vscode/
.idea/
*.swp
*.swo

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
=======
>>>>>>> 229e83784fd46ef5ffd97c8f8212653c1ebc9d7f
