{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "ios": {"buildConfiguration": "Debug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}}, "production": {"android": {"buildType": "app-bundle"}}, "production-apk": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}, "cache": {"disabled": false}}, "apk": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "standalone": {"developmentClient": false, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}}, "submit": {"production": {}}}