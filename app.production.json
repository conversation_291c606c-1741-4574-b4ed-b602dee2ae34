{"expo": {"name": "Workly", "slug": "Workly", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#2196F3"}, "assetBundlePatterns": ["assets/icon.png", "assets/adaptive-icon.png", "assets/splash-icon.png", "assets/notification-icon.png", "assets/favicon.png"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.workly.app", "infoPlist": {"NSLocationWhenInUseUsageDescription": "Ứng dụng cần quyền truy cập vị trí để xác định vị trí nhà và công ty cho tính năng cảnh báo thời tiết.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Ứng dụng cần quyền truy cập vị trí để xác định vị trí nhà và công ty cho tính năng cảnh báo thời tiết."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#2196F3"}, "edgeToEdgeEnabled": false, "package": "com.workly.app", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "POST_NOTIFICATIONS"], "versionCode": 1}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#2196F3", "defaultChannel": "default", "sounds": []}], "expo-font"], "notification": {"icon": "./assets/notification-icon.png", "color": "#2196F3", "iosDisplayInForeground": true, "androidMode": "default", "androidCollapsedTitle": "Workly"}, "extra": {"eas": {"projectId": "ef4e0d06-9b1f-4d5c-b680-23a75a2aad60"}}, "updates": {"fallbackToCacheTimeout": 0}, "runtimeVersion": {"policy": "sdkVersion"}}}