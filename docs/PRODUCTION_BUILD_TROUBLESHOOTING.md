# 🔧 Production Build Troubleshooting Guide

## 🎯 Vấn Đề Phổ Biến và Giải Pháp

### ❌ Eclipse/Java Workspace Error

#### **Lỗi:** `org.eclipse.core.internal.resources.ResourceException`

**Nguyên nhân:**
- Xung đột file trong thư mục android/
- Quyền truy cập file bị hạn chế
- VS Code workspace cache bị corrupt

**Giải pháp nhanh:**
```bash
# Option 1: Quick build (bypass workspace)
npm run build:quick

# Option 2: Fix workspace
npm run fix:android

# Option 3: Manual fix
# 1. Close VS Code
# 2. Delete android/ and ios/ folders
# 3. Run: npx expo prebuild --clean
# 4. Reopen VS Code
```

### ❌ APK Không Khởi Chạy Được

#### **Nguyên nhân có thể:**
1. **Xung đột dependencies**: React 19 không tương thích với Expo SDK 53
2. **New Architecture conflicts**: newArchEnabled=true gây xung đột
3. **Signing issues**: Sử dụng debug keystore cho production
4. **Missing permissions**: Permissions không được grant đúng cách
5. **Native modules conflicts**: Một số native modules không được build đúng

#### **Giải pháp:**
```bash
# 1. Fix dependencies
npm run fix:dependencies

# 2. Build với profile tối ưu
npm run build:production

# 3. Hoặc build trực tiếp
eas build --platform android --profile production-apk
```

### 🔍 Debug APK Trên Thiết Bị

#### **Kiểm tra logs:**
```bash
# Kết nối device và xem logs
adb logcat | grep -E "(Workly|ReactNative|FATAL|ERROR)"

# Hoặc filter theo package
adb logcat | grep com.workly.app
```

#### **Kiểm tra crash logs:**
```bash
# Xem crash logs
adb logcat | grep AndroidRuntime

# Xem memory issues
adb logcat | grep -E "(OutOfMemory|OOM)"
```

### 📱 Yêu Cầu Thiết Bị

#### **Android minimum requirements:**
- **OS Version**: Android 7.0+ (API 24+)
- **RAM**: 2GB+ recommended
- **Storage**: 100MB+ free space
- **Permissions**: Location, Notifications, Storage

#### **Kiểm tra device compatibility:**
```bash
# Kiểm tra Android version
adb shell getprop ro.build.version.release

# Kiểm tra API level
adb shell getprop ro.build.version.sdk

# Kiểm tra architecture
adb shell getprop ro.product.cpu.abi
```

### 🛠️ Build Issues

#### **EAS Build Fails:**
```bash
# Clear cache và rebuild
eas build --platform android --profile production-apk --clear-cache

# Kiểm tra build logs
eas build:list
```

#### **Gradle Build Fails:**
```bash
# Clean và rebuild
cd android
./gradlew clean
./gradlew assembleRelease

# Hoặc với debug info
./gradlew assembleRelease --info --stacktrace
```

#### **Metro Bundle Fails:**
```bash
# Clear Metro cache
npx expo start --clear

# Reset Metro cache
npx react-native start --reset-cache
```

### 🔧 Specific Error Solutions

#### **Error: "App keeps crashing on startup"**
```bash
# 1. Kiểm tra main activity
adb logcat | grep MainActivity

# 2. Kiểm tra React Native bridge
adb logcat | grep ReactNative

# 3. Rebuild với debug info
eas build --platform android --profile development
```

#### **Error: "White screen after splash"**
```bash
# 1. Kiểm tra bundle loading
adb logcat | grep "Bundle"

# 2. Kiểm tra JavaScript errors
adb logcat | grep "JS"

# 3. Test với development build
npm run build:dev-android
```

#### **Error: "Permissions denied"**
```bash
# Grant permissions manually
adb shell pm grant com.workly.app android.permission.ACCESS_FINE_LOCATION
adb shell pm grant com.workly.app android.permission.POST_NOTIFICATIONS
adb shell pm grant com.workly.app android.permission.VIBRATE
```

### 📋 Pre-Build Checklist

#### **Trước khi build:**
- [ ] Đã chạy `npm run fix:dependencies`
- [ ] Đã test trên Expo Go hoặc Development Build
- [ ] Đã kiểm tra tất cả permissions trong app.json
- [ ] Đã đảm bảo assets tồn tại (icons, splash screen)
- [ ] Đã login EAS CLI: `eas login`

#### **Cấu hình đã được fix:**
- [ ] React downgrade về 18.3.1
- [ ] newArchEnabled=false
- [ ] edgeToEdgeEnabled=false (consistent)
- [ ] Production build profile đã được tạo

### 🎯 Testing Strategy

#### **1. Development Build Test:**
```bash
# Build development version trước
npm run build:dev-android
# Test tất cả features
# Nếu OK, tiếp tục production build
```

#### **2. Production Build Test:**
```bash
# Build production APK
npm run build:production
# Test trên nhiều devices khác nhau
# Test offline functionality
```

#### **3. Performance Test:**
- Kiểm tra app startup time
- Test memory usage
- Test battery consumption
- Test với network slow/offline

### 🆘 Emergency Solutions

#### **Nếu tất cả đều fail:**
```bash
# 1. Reset project về trạng thái clean
git clean -fdx
npm install

# 2. Sử dụng Expo managed workflow
expo eject --npm

# 3. Build với bare workflow
npx expo run:android --variant release
```

#### **Alternative build methods:**
```bash
# 1. Local build với Android Studio
npx expo prebuild --platform android
# Mở android/ trong Android Studio và build

# 2. EAS local build
eas build --platform android --local

# 3. Manual Gradle build
cd android && ./gradlew assembleRelease
```

### 📞 Support Resources

- **Expo Discord**: https://chat.expo.dev
- **EAS Build Docs**: https://docs.expo.dev/build/introduction/
- **React Native Troubleshooting**: https://reactnative.dev/docs/troubleshooting
- **Android Debug Guide**: https://developer.android.com/studio/debug
