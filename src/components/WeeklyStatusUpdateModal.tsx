import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Modal, Portal, Text, Button, useTheme, List, Divider } from 'react-native-paper';
import { format, parseISO, isToday, isPast, isFuture } from 'date-fns';
import { vi, enUS } from 'date-fns/locale';
import { DailyWorkStatus, Shift } from '../types';
import { WEEKLY_STATUS } from '../constants';
import { useApp } from '../contexts/AppContext';
import { t } from '../i18n';

interface WeeklyStatusUpdateModalProps {
  visible: boolean;
  onDismiss: () => void;
  date: string;
  currentStatus: DailyWorkStatus | null;
  onStatusUpdate: (status: DailyWorkStatus['status']) => Promise<void>;
  onRecalculateFromLogs?: () => Promise<void>;
  onEditTime?: () => Promise<void>;
  onClearManualStatus?: () => Promise<void>;
}

/**
 * <PERSON><PERSON> cập nhật trạng thái tuần theo yêu cầu mới
 * - <PERSON><PERSON>n thị các lựa chọn khác nhau cho ngày quá khứ/hiện tại vs tương lai
 * - Giao diện đơn giản, dễ sử dụng
 */
export function WeeklyStatusUpdateModal({
  visible,
  onDismiss,
  date,
  currentStatus,
  onStatusUpdate,
  onRecalculateFromLogs,
  onEditTime,
  onClearManualStatus,
}: WeeklyStatusUpdateModalProps) {
  const theme = useTheme();
  const { state } = useApp();
  const currentLanguage = state.settings?.language || 'vi';

  // Xác định loại ngày
  const dateObj = parseISO(date);
  const isPastOrCurrent = isToday(dateObj) || isPast(dateObj);
  const isFutureDate = isFuture(dateObj) && !isToday(dateObj);

  // Format ngày hiển thị
  const formattedDate = format(dateObj, 'EEEE, dd/MM/yyyy', { 
    locale: currentLanguage === 'vi' ? vi : enUS 
  });

  // Kiểm tra xem có trạng thái nghỉ thủ công không
  const hasManualLeaveStatus = currentStatus && [
    'NGHI_PHEP', 'NGHI_BENH', 'NGHI_LE', 'VANG_MAT', 'CONG_TAC'
  ].includes(currentStatus.status);

  const handleStatusSelect = async (status: DailyWorkStatus['status']) => {
    try {
      await onStatusUpdate(status);
      onDismiss();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleRecalculate = async () => {
    try {
      if (onRecalculateFromLogs) {
        await onRecalculateFromLogs();
      }
      onDismiss();
    } catch (error) {
      console.error('Error recalculating:', error);
    }
  };

  const handleEditTime = async () => {
    try {
      if (onEditTime) {
        await onEditTime();
      }
      onDismiss();
    } catch (error) {
      console.error('Error editing time:', error);
    }
  };

  const handleClearManualStatus = async () => {
    try {
      if (onClearManualStatus) {
        await onClearManualStatus();
      }
      onDismiss();
    } catch (error) {
      console.error('Error clearing manual status:', error);
    }
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <ScrollView style={styles.scrollView}>
          {/* Header */}
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            {isFutureDate 
              ? `Đặt trạng thái nghỉ cho ${formattedDate}`
              : `Cập nhật trạng thái cho ${formattedDate}`
            }
          </Text>

          <Divider style={styles.divider} />

          {/* Lựa chọn cho ngày quá khứ/hiện tại */}
          {isPastOrCurrent && (
            <>
              <List.Item
                title="Tính theo Chấm công"
                description="Cho phép hệ thống tự động tính toán lại status và giờ công"
                left={(props) => <List.Icon {...props} icon="calculator" />}
                onPress={handleRecalculate}
                style={styles.listItem}
              />

              <List.Item
                title="Chỉnh sửa Giờ Chấm công..."
                description="Nhập/sửa trực tiếp checkInTime và checkOutTime"
                left={(props) => <List.Icon {...props} icon="clock-edit" />}
                onPress={handleEditTime}
                style={styles.listItem}
              />

              <Divider style={styles.divider} />
            </>
          )}

          {/* Các trạng thái nghỉ */}
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurfaceVariant }]}>
            Trạng thái nghỉ
          </Text>

          <List.Item
            title="Nghỉ Phép"
            left={(props) => <List.Icon {...props} icon="beach" />}
            onPress={() => handleStatusSelect('NGHI_PHEP')}
            style={styles.listItem}
          />

          <List.Item
            title="Nghỉ Bệnh"
            left={(props) => <List.Icon {...props} icon="hospital-box" />}
            onPress={() => handleStatusSelect('NGHI_BENH')}
            style={styles.listItem}
          />

          <List.Item
            title="Nghỉ Lễ"
            left={(props) => <List.Icon {...props} icon="flag" />}
            onPress={() => handleStatusSelect('NGHI_LE')}
            style={styles.listItem}
          />

          <List.Item
            title="Vắng Mặt"
            left={(props) => <List.Icon {...props} icon="close-circle" />}
            onPress={() => handleStatusSelect('VANG_MAT')}
            style={styles.listItem}
          />

          <List.Item
            title="Công tác"
            left={(props) => <List.Icon {...props} icon="airplane" />}
            onPress={() => handleStatusSelect('CONG_TAC')}
            style={styles.listItem}
          />

          {/* Xóa trạng thái nghỉ thủ công */}
          {hasManualLeaveStatus && (
            <>
              <Divider style={styles.divider} />
              <List.Item
                title="Xóa Trạng thái Nghỉ / Tính lại Công"
                description="Xóa trạng thái nghỉ thủ công và tính lại dựa trên log"
                left={(props) => <List.Icon {...props} icon="delete" />}
                onPress={handleClearManualStatus}
                style={styles.listItem}
                titleStyle={{ color: theme.colors.error }}
              />
            </>
          )}

          {/* Nút Hủy */}
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.cancelButton}
          >
            Hủy
          </Button>
        </ScrollView>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
  scrollView: {
    maxHeight: 500,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  listItem: {
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  cancelButton: {
    margin: 16,
    marginTop: 24,
  },
});
